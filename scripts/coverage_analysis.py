#!/usr/bin/env python3
"""
Practice Area Coverage Analysis

This script provides a detailed analysis of practice area coverage
across all enabled jurisdictions.
"""

import sys
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.harvesting.harvesting_config import get_harvesting_config

def main():
    print("📊 COMPREHENSIVE PRACTICE AREA COVERAGE ANALYSIS")
    print("=" * 70)
    
    config = get_harvesting_config()
    enabled = config.get_enabled_jurisdictions()
    
    # Collect all practice areas and which jurisdictions have them
    all_practice_areas = set()
    jurisdiction_practice_areas = {}
    
    for jurisdiction in enabled:
        jconfig = config.get_jurisdiction_config(jurisdiction)
        areas = jconfig.practice_areas
        all_practice_areas.update(areas)
        jurisdiction_practice_areas[jurisdiction] = areas
    
    print(f"Total Jurisdictions: {len(enabled)}")
    print(f"Total Practice Areas: {len(all_practice_areas)}")
    print()
    
    # Calculate coverage for each practice area
    coverage_data = []
    for area in sorted(all_practice_areas):
        has_area = []
        missing_area = []
        
        for jurisdiction in enabled:
            if area in jurisdiction_practice_areas[jurisdiction]:
                has_area.append(jurisdiction.upper())
            else:
                missing_area.append(jurisdiction.upper())
        
        coverage = len(has_area) / len(enabled) * 100
        coverage_data.append({
            'area': area,
            'coverage': coverage,
            'has': has_area,
            'missing': missing_area,
            'count': len(has_area),
            'total': len(enabled)
        })
    
    # Sort by coverage percentage (highest first)
    coverage_data.sort(key=lambda x: x['coverage'], reverse=True)
    
    print("🎯 PRACTICE AREA COVERAGE BREAKDOWN:")
    print("=" * 70)
    
    for data in coverage_data:
        area_name = data['area'].replace('_', ' ').title()
        coverage = data['coverage']
        count = data['count']
        total = data['total']
        
        # Coverage indicator
        if coverage == 100:
            indicator = "🟢 COMPLETE"
        elif coverage >= 80:
            indicator = "🟡 GOOD"
        elif coverage >= 60:
            indicator = "🟠 PARTIAL"
        else:
            indicator = "🔴 LIMITED"
        
        print(f"{area_name}:")
        print(f"  {indicator} Coverage: {coverage:.0f}% ({count}/{total} jurisdictions)")
        print(f"  ✅ Available in: {', '.join(data['has'])}")
        if data['missing']:
            print(f"  ❌ Missing from: {', '.join(data['missing'])}")
        print()
    
    # Summary statistics
    total_coverage = sum(d['coverage'] for d in coverage_data) / len(coverage_data)
    complete_areas = len([d for d in coverage_data if d['coverage'] == 100])
    good_areas = len([d for d in coverage_data if d['coverage'] >= 80])
    
    print("📈 COVERAGE SUMMARY:")
    print("=" * 40)
    print(f"Overall Average Coverage: {total_coverage:.1f}%")
    print(f"Complete Coverage (100%): {complete_areas}/{len(coverage_data)} areas")
    print(f"Good Coverage (≥80%): {good_areas}/{len(coverage_data)} areas")
    print()
    
    # Jurisdiction-specific analysis
    print("🗺️ JURISDICTION-SPECIFIC ANALYSIS:")
    print("=" * 50)
    
    for jurisdiction in enabled:
        jconfig = config.get_jurisdiction_config(jurisdiction)
        areas = jconfig.practice_areas
        
        print(f"{jurisdiction.upper()} ({jconfig.name}):")
        print(f"  Practice Areas: {len(areas)}")
        print(f"  Areas: {', '.join([a.replace('_', ' ').title() for a in sorted(areas)])}")
        print()
    
    # Recommendations
    print("💡 RECOMMENDATIONS:")
    print("=" * 30)
    
    limited_areas = [d for d in coverage_data if d['coverage'] < 80]
    if limited_areas:
        print("Areas with limited coverage that could be expanded:")
        for data in limited_areas:
            area_name = data['area'].replace('_', ' ').title()
            print(f"  • {area_name}: Add to {', '.join(data['missing'])}")
    else:
        print("✅ All practice areas have good coverage (≥80%)!")
    
    print()
    print("🎉 Configuration Analysis Complete!")

if __name__ == "__main__":
    main()
